<?php

declare(strict_types=1);

namespace Tests\Unit\Services\AI;

use App\Services\AI\TemplateGenerator;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use Guz<PERSON><PERSON>ttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;
use Mo<PERSON>y;
use ReflectionClass;

use function Pest\Laravel\seed;

// This beforeEach runs before each test in this file.
beforeEach(function () {
    seed(); // Seeds the database if needed.

    // Set up mock configuration values for the tests.
    Config::set('message_check.token', 'test-token');
    Config::set('message_check.url', 'https://test-api.example.com/');
    Config::set('message_check.model', 'test-model');
});

/**
 * Creates a partial mock of TemplateGenerator with a mocked Guzzle client.
 * This allows testing the class's logic without making real HTTP requests.
 *
 * @param array<int, Response|RequestException> $responses The mock responses or exceptions for the HTTP client.
 * @return TemplateGenerator
 */
function createGeneratorWithMockedClient(array $responses): TemplateGenerator
{
    // Create a mock handler for Guzzle.
    $mock = new MockHandler($responses);
    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    // Create a partial mock (spy) of TemplateGenerator.
    // This lets us mock specific methods (like getClient) while keeping the original logic for others.
    $generator = Mockery::spy(TemplateGenerator::class)->makePartial();
    $generator->shouldReceive('getClient')->andReturn($client);

    return $generator;
}

it('can generate a template successfully', function () {
    // 1. Arrange
    // This is the expected JSON response from the AI API.
    $mockApiResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            // The 'text' field contains a JSON string with the template details.
                            'text' => json_encode([
                                'content' => 'مرحباً {{name}}! نرحب بك في {{company_name}}.',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                    ['name' => 'company_name', 'description' => 'Company name'],
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    // Create the generator with a mocked client that will return a successful response.
    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockApiResponse)),
    ]);

    // 2. Act
    // Call the generate method with the required arguments.
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    // Check if the generation was successful and the content is correct.
    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('مرحباً {{name}}! نرحب بك في {{company_name}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('company_name');
});

it('handles API failure gracefully', function () {
    // 1. Arrange
    // Create the generator with a mocked client that simulates a server error.
    $generator = createGeneratorWithMockedClient([
        new Response(500, [], 'Internal Server Error'),
    ]);

    // 2. Act
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    // Verify that the method returns a failure state.
    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('API request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles request exception gracefully', function () {
    // 1. Arrange
    // Create a generator that simulates a network-level exception.
    $generator = createGeneratorWithMockedClient([
        new RequestException('Connection timeout', new Request('POST', 'test')),
    ]);

    // 2. Act
    // The generate method should catch the GuzzleException and return a structured error.
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('Request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles invalid JSON response gracefully', function () {
    // 1. Arrange
    // Simulate a response where the API returns a malformed JSON string.
    $mockApiResponse = [
        'candidates' => [['content' => ['parts' => [['text' => 'Invalid JSON { response']]]]],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockApiResponse)),
    ]);

    // 2. Act
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    // The parser should fail and return a specific error.
    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Failed to parse response');
});

it('extracts parameters from content when not provided in response', function () {
    // 1. Arrange
    // Simulate a response where the `parameters` array is missing.
    $mockApiResponse = [
        'candidates' => [['content' => ['parts' => [['text' => json_encode([
            'content' => 'Hello {{name}}, your code is {{code}}.',
        ])]]]]],
    ];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockApiResponse)),
    ]);

    // 2. Act
    $result = $generator->generate('verification', 'Send verification code', 'en');

    // 3. Assert
    // The system should fall back to extracting parameters from the `{{...}}` placeholders.
    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}, your code is {{code}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('code');
});

it('builds correct prompt for different template types and languages', function () {
    // 1. Arrange
    $generator = new TemplateGenerator();
    // Use PHP's Reflection API to test the private `buildPrompt` method.
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('buildPrompt');
    $method->setAccessible(true); // Make the private method accessible for the test.

    // 2. Act
    $prompt = $method->invoke($generator, 'welcome', 'Welcome message for new users', 'ar');

    // 3. Assert
    expect($prompt)->toContain('Generate the template in Arabic language.');
    expect($prompt)->toContain('Template Type: welcome');
    expect($prompt)->toContain('Description: Welcome message for new users');
});


it('handles missing text in API response', function () {
    // 1. Arrange
    // Simulate a valid response, but the `text` field is null.
    $mockApiResponse = ['candidates' => [['content' => ['parts' => [['text' => null]]]]]];
    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockApiResponse)),
    ]);

    // 2. Act
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Missing text in response');
});

it('handles response with invalid content format', function () {
    // 1. Arrange
    // The response is valid JSON, but it's missing the expected 'content' key.
    $mockApiResponse = ['candidates' => [['content' => ['parts' => [['text' => json_encode(['invalid_key' => 'structure'])]]]]]];

    $generator = createGeneratorWithMockedClient([
        new Response(200, [], json_encode($mockApiResponse)),
    ]);

    // 2. Act
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Invalid response format');
});

it('handles response with markdown formatting', function () {
    // 1. Arrange
    // Simulate a response wrapped in Markdown code blocks.
    $apiText = "```json\n" . json_encode([
            'content' => 'Hello {{name}}!',
            'parameters' => [['name' => 'name', 'description' => 'User name']],
        ]) . "\n```";
    $mockApiResponse = ['candidates' => [['content' => ['parts' => [['text' => $apiText]]]]]];
    $generator = createGeneratorWithMockedClient([new Response(200, [], json_encode($mockApiResponse))]);

    // 2. Act
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    // The parser should correctly strip the markdown and parse the JSON.
    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}!');
    expect($result['parameters'])->toHaveCount(1);
});

it('handles response with malformed parameter objects', function () {
    // 1. Arrange
    // The parameters array contains various invalid formats.
    $apiText = json_encode([
        'content' => 'Hello {{name}} and {{age}}!',
        'parameters' => [
            ['name' => 'name'], // Missing description
            'invalid_param', // Not an object
            ['description' => 'Age'], // Missing name
            ['name' => 'valid', 'description' => 'Valid param'], // This one is valid
        ],
    ]);
    $mockApiResponse = ['candidates' => [['content' => ['parts' => [['text' => $apiText]]]]]];
    $generator = createGeneratorWithMockedClient([new Response(200, [], json_encode($mockApiResponse))]);

    // 2. Act
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    // 3. Assert
    // Only the correctly structured parameter should be in the final result.
    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}} and {{age}}!');
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('valid');
});
