<?php

declare(strict_types=1);

use App\Filament\Company\Resources\MessageTemplateResource;
use App\Models\Company;
use App\Models\Project;
use App\Models\User;
use App\Services\AI\TemplateGenerator;
use Filament\Facades\Filament;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;
use Livewire\Livewire;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\seed;

beforeEach(function () {
    seed();

    $this->user = User::factory()->create();
    $this->company = Company::factory()->create();
    $this->project = Project::factory()->withSubscription('SMS Starter 1')->create([
        'company_id' => $this->company->id,
    ]);

    $this->user->companies()->attach($this->company);

    // Set up test configuration
    Config::set('message_check.token', 'test-token');
    Config::set('message_check.url', 'https://test-api.example.com/');
    Config::set('message_check.model', 'test-model');
});

// Helper function to create a testable TemplateGenerator
function createMockTemplateGenerator(MockHandler $mock): object
{
    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    return new class($client) extends TemplateGenerator
    {
        private Client $mockClient;

        public function __construct(Client $mockClient)
        {
            parent::__construct();
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };
}

it('can access generate action', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $component = Livewire::test(MessageTemplateResource\Pages\CreateMessageTemplate::class);

    // Test that the generate action exists
    $component->assertActionExists('generate');
});

it('validates generate action form data', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $component = Livewire::test(MessageTemplateResource\Pages\CreateMessageTemplate::class);

    // Test validation with missing required fields
    $component->callAction('generate', [])
        ->assertHasActionErrors([
            'type' => 'required',
            'language' => 'required',
            'description' => 'required',
        ]);
});

it('tests successful template generation workflow', function () {
    // Mock successful API response
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'مرحباً {{name}}! نرحب بك في {{company_name}}.',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                    ['name' => 'company_name', 'description' => 'Company name'],
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('مرحباً {{name}}! نرحب بك في {{company_name}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('company_name');
});

it('tests template generation failure handling', function () {
    // Mock API failure response
    $mock = new MockHandler([
        new Response(500, [], 'Internal Server Error'),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('API request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests request exception handling', function () {
    // Mock request exception
    $mock = new MockHandler([
        new RequestException('Connection timeout', new Request('POST', 'test')),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('Request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests parameter extraction from content', function () {
    // Mock response without parameters
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{first_name}} {{last_name}}! Your order {{order_id}} is ready.',
                                'parameters' => [], // Empty parameters to test extraction
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('notification', 'Order notification', 'en');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{first_name}} {{last_name}}! Your order {{order_id}} is ready.');
    expect($result['parameters'])->toHaveCount(3);
    expect($result['parameters'][0]['name'])->toBe('first_name');
    expect($result['parameters'][1]['name'])->toBe('last_name');
    expect($result['parameters'][2]['name'])->toBe('order_id');
});

it('tests content with no parameters', function () {
    // Mock response with content but no parameters
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Welcome to our service!',
                                'parameters' => [],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Welcome to our service!');
    expect($result['parameters'])->toHaveCount(0);
});

it('tests invalid JSON response handling', function () {
    // Mock response with invalid JSON
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => '{"invalid": json}', // Invalid JSON syntax
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Failed to parse response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests missing text in API response', function () {
    // Mock response with missing text
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => null,
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Missing text in response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests invalid response format handling', function () {
    // Mock response with invalid content structure
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'invalid' => 'structure',
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Invalid response format');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('tests markdown formatted response handling', function () {
    // Mock response with markdown-formatted JSON
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => "```json\n".json_encode([
                                'content' => 'Hello {{name}}!',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                ],
                            ])."\n```",
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}!');
    expect($result['parameters'])->toHaveCount(1);
    expect($result['parameters'][0]['name'])->toBe('name');
});

it('tests malformed parameter objects handling', function () {
    // Mock response with malformed parameter objects
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}} and {{age}}!',
                                'parameters' => [
                                    ['name' => 'name'], // Missing description
                                    'invalid_param', // Not an object
                                    ['description' => 'Age'], // Missing name
                                    ['name' => 'valid', 'description' => 'Valid param'], // Valid
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}} and {{age}}!');
    expect($result['parameters'])->toHaveCount(1); // Only valid parameter should be included
    expect($result['parameters'][0]['name'])->toBe('valid');
    expect($result['parameters'][0]['description'])->toBe('Valid param');
});

it('tests empty response from API', function () {
    $mock = new MockHandler([
        new Response(200, [], ''),
    ]);

    $generator = createMockTemplateGenerator($mock);
    $result = $generator->generate('welcome', 'Welcome new users', 'ar');

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('JSON parsing failed');
});

it('tests different template types and languages', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private getTemplateExamples method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('getTemplateExamples');
    $method->setAccessible(true);

    $examples = $method->invoke($generator, 'reminder', 'ar');

    expect($examples)->toContain('تذكير'); // Arabic word for reminder
    expect($examples)->toContain('{{date}}');
    expect($examples)->toContain('{{event}}');
    expect($examples)->toContain('{{time}}');
});

it('tests buildPrompt method for different languages', function () {
    $generator = new TemplateGenerator();

    // Use reflection to test the private buildPrompt method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('buildPrompt');
    $method->setAccessible(true);

    $prompt = $method->invoke($generator, 'notification', 'Send notification to users', 'en');

    expect($prompt)->toContain('Generate the template in English language');
    expect($prompt)->toContain('Template Type: notification');
    expect($prompt)->toContain('Description: Send notification to users');
});
