<?php

declare(strict_types=1);

use App\Filament\Company\Resources\MessageTemplateResource\Pages\CreateMessageTemplate;
use App\Models\Company;
use App\Models\User;
use App\Services\AI\TemplateGenerator;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function () {
    seed();

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('test generate method returns correct data', function () {

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $templateGenerator = Mockery::mock(TemplateGenerator::class)->makePartial();
    $templateGenerator->shouldReceive('generate')->andReturn([
        'success' => true,
        'content' => 'Hello {{name}}!',
        'parameters' => [
            ['name' => 'name', 'description' => 'User name'],
        ],
    ]);

    livewire(CreateMessageTemplate::class)
        ->callAction('generate', [
            'type' => 'welcome',
            'description' => 'Welcome new users',
            'language' => 'ar',
        ]);

});

it('test generate method unsuccessful', function () {

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $templateGenerator = Mockery::mock(TemplateGenerator::class)->makePartial();
    $templateGenerator->shouldReceive('generate')->andReturn([
        'success' => false,
        'error' => 'Failed to generate template',
        'content' => '',
        'parameters' => [],
    ]);

    livewire(CreateMessageTemplate::class)
        ->callAction('generate', [
            'type' => 'welcome',
            'description' => 'Welcome new users',
            'language' => 'ar',
        ])->assertNotified();

});

it('test catch error on generate', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $templateGenerator = Mockery::mock(TemplateGenerator::class)->makePartial();
    $templateGenerator->shouldReceive('generate')->andThrow(new Exception('Failed to generate template'));

    livewire(CreateMessageTemplate::class)
        ->callAction('generate', [
            'type' => 'welcome',
            'description' => 'Welcome new users',
            'language' => 'ar',
        ])->assertNotified();
});
